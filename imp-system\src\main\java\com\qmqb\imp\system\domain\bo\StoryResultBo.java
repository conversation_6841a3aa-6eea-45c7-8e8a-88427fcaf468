package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.dto.BasePageDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;


import java.util.Date;


/**
 * 需求成果业务对象 tb_story_result
 *
 * <AUTHOR>
 * @date 2025-08-13
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class StoryResultBo extends BasePageDTO {

    /**
     * 需求id
     */
    private Long id;

    /**
     * 成果id
     */
    private Long resultId;

    /**
     * 成果编码
     */
    private String resultCode;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 需求标题
     */
    private String title;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 所处阶段
     */
    private String stage;

    /**
     * 创建人
     */
    private String openedBy;

    /**
     * 创建时间
     */
    private Date openedDate;

    /**
     * 跟进人
     */
    private String assignedTo;

    /**
     * 跟进时间
     */
    private Date assignedDate;

    /**
     * 评审人
     */
    private String reviewedBy;

    /**
     * 评审时间
     */
    private Date reviewedDate;

    /**
     * 关闭人
     */
    private String closedBy;

    /**
     * 关闭时间
     */
    private Date closedDate;

    /**
     * 关闭原因
     */
    private String closedReason;

    /**
     * bug数量
     */
    private Integer bugCount;

    /**
     * 用例数量
     */
    private Integer caseCount;

    /**
     * 任务数量
     */
    private Integer taskCount;

    /**
     * 发布数
     */
    private Integer releaseCount;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date beginDate;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 是否加入成果
     *
     */
    private Boolean joinResult;

}
