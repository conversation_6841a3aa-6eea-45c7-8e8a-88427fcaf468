package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.common.utils.poi.ExcelUtil;
import com.qmqb.imp.system.domain.bo.ProjectResultBo;
import com.qmqb.imp.system.domain.vo.ProjectResultVo;
import com.qmqb.imp.system.service.IProjectResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 项目成果管理
 *
 * <AUTHOR>
 * @date 2025-01-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/projectResult")
public class ProjectResultController extends BaseController {

    private final IProjectResultService projectResultService;

    /**
     * 查询项目成果表列表
     */
    @SaCheckPermission("system:projectResult:list")
    @GetMapping("/list")
    public TableDataInfo<ProjectResultVo> list(ProjectResultBo bo, PageQuery pageQuery) {
        return projectResultService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出项目成果表列表
     */
    @SaCheckPermission("system:projectResult:export")
    @Log(title = "项目成果表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProjectResultBo bo, HttpServletResponse response) {
        List<ProjectResultVo> list = projectResultService.queryList(bo);
        ExcelUtil.exportExcel(list, "项目成果表", ProjectResultVo.class, response);
    }

    /**
     * 获取项目成果表详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:projectResult:query")
    @GetMapping("/{id}")
    public R<ProjectResultVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(projectResultService.queryById(id));
    }

    /**
     * 新增项目成果表
     */
    @SaCheckPermission("system:projectResult:add")
    @Log(title = "项目成果表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProjectResultBo bo) {
        return toAjax(projectResultService.insertByBo(bo));
    }

    /**
     * 修改项目成果表
     */
    @SaCheckPermission("system:projectResult:edit")
    @Log(title = "项目成果表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProjectResultBo bo) {
        return toAjax(projectResultService.updateByBo(bo));
    }

    /**同步项目成果 */
    @SaCheckPermission("system:projectResult:sync")
    @Log(title = "项目成果表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sync")
    public R<Boolean> sync(@RequestBody ProjectResultBo bo) {
        return R.ok(projectResultService.sync(bo));
    }

    /**
     * 删除项目成果表
     */
    @SaCheckPermission("system:projectResult:remove")
    @Log(title = "项目成果表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(projectResultService.deleteWithValidByIds(Arrays.asList(ids), true));
    }

    /**
     * 归档项目成果
     */
    @SaCheckPermission("system:projectResult:edit")
    @Log(title = "项目成果表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/archive/{id}")
    public R<Boolean> archive(@NotNull(message = "主键不能为空")
                          @PathVariable Long id) {
        return R.ok(projectResultService.archiveById(id));
    }

    /**
     * 生成项目成果
     */
    @SaCheckPermission("system:projectResult:generate")
    @Log(title = "生成项目成果")
    @RepeatSubmit()
    @PostMapping("/generateResults")
    public R<Void> generateResults(@RequestBody ProjectResultBo bo) {
        return toAjax(projectResultService.generateResults(bo));
    }

    /**
     * 加入项目成果
     */
    @SaCheckPermission("system:projectResult:join")
    @Log(title = "加入项目成果")
    @RepeatSubmit()
    @PostMapping("/joinResults")
    public R<Void> joinResults(@RequestBody ProjectResultBo bo) {
        return toAjax(projectResultService.joinResults(bo));
    }

}
