package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.mapper.ProjectResultMapper;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import org.springframework.util.StopWatch;

import java.util.*;

/**
 * 项目成果预警通知定时器
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectResultWarnService {

    private final ProjectResultMapper projectResultMapper;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;

    final String milestoneRequirementsStr="milestone_requirements";
    final String requirementsProgressStr="requirements_progress";
    final String devManpowerStr="dev_manpower";
    final String testManpowerStr="test_manpower";
    final String devWorkloadStr="dev_workload";
    final String testWorkloadStr="test_workload";

    @TraceId("项目成果预警通知定时任务")
    @XxlJob("projectResultWarnServiceJobHandler")
    public ReturnT<String> projectResultWarnServiceJobHandler(String param) {
        try {
            //节假日不执行定时任务
            if(HolidayUtil.isHoliday(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.getNowDate()))) {
                log.info("节假日不执行定时任务");
                return ReturnT.SUCCESS;
            }

            XxlJobLogger.log("开始执行项目成果预警通知定时任务...");
            log.info("开始执行项目成果预警通知定时任务");
            val sw = new StopWatch();
            sw.start();

            // 统计缺失信息的项目成果数量
            List<Map<String, Object>> incompleteProjectResults = getIncompleteProjectResults();

            // 发送通知
            if (CollectionUtil.isNotEmpty(incompleteProjectResults)) {
                int count = incompleteProjectResults.size();
                send(Constants.PROJECT_RESULT_INFO_LACK_TAG, count);
                XxlJobLogger.log("发送项目成果预警通知，缺失信息的项目成果数量：{}", count);
                log.info("发送项目成果预警通知，缺失信息的项目成果数量：{}", count);
            } else {
                log.info("所有项目成果信息均已完善，无需发送通知");
                XxlJobLogger.log("所有项目成果信息均已完善，无需发送通知");
            }

            sw.stop();
            XxlJobLogger.log("项目成果预警通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());
            log.info("项目成果预警通知定时任务执行结束,耗时:{}ms", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("项目成果预警通知定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 获取信息不完整的项目成果列表
     * 统计逻辑：每个评定标准内的所有字段都为空才算该标准缺失
     * - 项目里程碑 = 完成评审时间+完成开发时间+完成测试验收时间+完成上线时间（4个字段都为空才算空）
     * - 任务说明/进度 = 需求评审进度+开发进度+测试验收进度（3个字段都为空才算空）
     * - 干系人 = 产品+开发组+测试组（3个字段都为空才算空，考虑空字符串）
     * - 投入人力 = 开发+测试（2个字段都为空才算空，考虑0值）
     * - 工作量(日) = 开发+测试（2个字段都为空才算空，考虑0值）
     *
     * @return 信息不完整的项目成果列表
     */
    private List<Map<String, Object>> getIncompleteProjectResults() {
        try {
            // 一次性查询所有数据，SQL已经计算好缺失字段
            List<Map<String, Object>> allResults = projectResultMapper.selectAllIncompleteProjectResults();

            log.info("总共查询到信息不完整的项目成果：{} 条", allResults.size());
            return allResults;
        } catch (Exception e) {
            log.error("查询信息不完整的项目成果异常", e);
            return new ArrayList<>();
        }
    }

    /**
     * 发送预警
     *
     * @param template 模板
     * @param count    缺失信息的项目成果数量
     */
    private void send(String template, int count) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("count", count);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder()
                .url(dingTalkConfig.getPmRobotUrl())
                .msgtype("text")
                .content(content)
                .build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
