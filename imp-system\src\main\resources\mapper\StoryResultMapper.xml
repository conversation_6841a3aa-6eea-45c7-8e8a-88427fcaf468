<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.StoryResultMapper">

    <resultMap type="com.qmqb.imp.system.domain.StoryResult" id="StoryResultResult">
        <result property="id" column="id"/>
        <result property="resultId" column="result_id"/>
        <result property="resultCode" column="result_code"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="title" column="title"/>
        <result property="status" column="status"/>
        <result property="stage" column="stage"/>
        <result property="openedBy" column="opened_by"/>
        <result property="openedDate" column="opened_date"/>
        <result property="assignedTo" column="assigned_to"/>
        <result property="assignedDate" column="assigned_date"/>
        <result property="reviewedBy" column="reviewed_by"/>
        <result property="reviewedDate" column="reviewed_date"/>
        <result property="closedBy" column="closed_by"/>
        <result property="closedDate" column="closed_date"/>
        <result property="closedReason" column="closed_reason"/>
        <result property="bugCount" column="bug_count"/>
        <result property="caseCount" column="case_count"/>
        <result property="taskCount" column="task_count"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="pageList" resultType="com.qmqb.imp.system.domain.vo.StoryResultVo">
        select * from tb_story_result
        where del_flag = 0
        <if test="request.productId != null">
            and product_id = #{request.productId}
        </if>
        <if test="request.status != null and request.status != '' and request.status != 'all'">
            and status = #{request.status}
        </if>
        <if test="request.beginDate != null ">
            AND opened_date  <![CDATA[ >= ]]> #{request.beginDate}
        </if>
        <if test="request.endDate != null">
            AND opened_date  <![CDATA[ <= ]]>  #{request.endDate}
        </if>
        <if test="request.title != null and request.title != ''">
            and title like concat('%',#{request.title},'%')
        </if>
        <if test="request.id != null">
            and id = #{request.id}
        </if>
        <if test="request.resultCode != null and request.resultCode != ''">
            and result_code = #{request.resultCode}
        </if>
        <if test="request.assignedTo != null and request.assignedTo != ''">
            and assigned_to = #{request.assignedTo}
        </if>
        <if test="request.reviewedBy != null and request.reviewedBy != ''">
            and reviewed_by like concat('%',#{request.reviewedBy},'%')
        </if>
        <if test="request.joinResult != null and request.joinResult = false">
            and result_id is null
        </if>
        <if test="request.orderByField != null and request.orderByField != ''
        and request.orderRule != null and request.orderRule != ''">
            order by ${request.orderByField} ${request.orderRule}
        </if>
    </select>

    <!-- 根据成果ID批量清空需求的成果关联信息 -->
    <update id="clearResultInfoByResultIds">
        UPDATE tb_story_result
        SET result_id = NULL, result_code = NULL, updated_time = NOW()
        WHERE result_id IN
        <foreach collection="resultIds" item="resultId" open="(" separator="," close=")">
            #{resultId}
        </foreach>
        AND del_flag = 0
    </update>

</mapper>
